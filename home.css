.hero {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2em;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-x: hidden;
}



/* home - hero img holder */
.hero-img-holder {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2em;
}

.hero-img-holder .hero-img {
  position: relative;
  width: 100%;
  height: 100%;
  transform: translateY(-110%) scale(0.25) rotate(0);
  border: 0.3em solid var(--fg);
  border-radius: 2em;
  overflow: hidden;
  transition: transform 0.1s ease-out;
}

.hero-img-holder .hero-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.spacer {
  height: 100vh;
  background-color: var(--bg);
}
