{"v": "5.1.6", "fr": 60, "ip": 0, "op": 301, "w": 800, "h": 600, "nm": "starfish", "ddd": 0, "assets": [{"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [43.289, 27.563, 0], "ix": 2}, "a": {"a": 0, "k": [-18, -28, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 1, "k": [{"i": {"x": 0.52, "y": 1}, "o": {"x": 0.45, "y": 0}, "n": "0p52_1_0p45_0", "t": 26, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.164, -38.188], [-36.914, -38.188], [-36.914, -17.438], [-0.164, -17.438]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.164, -22.438], [-36.914, -22.438], [-36.914, -1.688], [-0.164, -1.688]], "c": true}]}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.48, "y": 0}, "n": "0p57_1_0p48_0", "t": 31.588, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.164, -22.438], [-36.914, -22.438], [-36.914, -1.688], [-0.164, -1.688]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.164, -38.188], [-36.914, -38.188], [-36.914, -17.438], [-0.164, -17.438]], "c": true}]}, {"t": 44}], "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [18, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.235294117647, 0.232525963877, 0.229757780187, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-18, -28], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 140, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 3", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [95.32, 66.031, 0], "ix": 2}, "a": {"a": 0, "k": [-18, -28, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [18, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.235294117647, 0.232525963877, 0.229757780187, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-18, -28], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 4", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [66.344, 96.313, 0], "ix": 2}, "a": {"a": 0, "k": [-18, -22.44, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "n": ["0p833_0p833_0p167_0p167", "0p833_0p833_0p167_0p167", "0p833_0p833_0p167_0p167"], "t": 114, "s": [150, 239.984, 100], "e": [150, 239.984, 100]}, {"t": 126}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.534, -0.418]], "o": [[0, 0], [0, 0], [10.726, 0.389]], "v": [[12.5, 0], [-12.5, 0], [0, 11.111]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.235294117647, 0.232525963877, 0.229757780187, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-18, -28], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "eye", "parent": 4, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [38.031, 73.969, 0], "ix": 2}, "a": {"a": 0, "k": [47, 35.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [2.333]}, {"t": 140}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = loopOut('cycle');"}, "w": 94, "h": 71, "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "main", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.89, "y": 0}, "n": "0p833_0p833_0p89_0", "t": 0, "s": [1228.969, 286.031, 0], "e": [421.969, 286.031, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 69, "s": [421.969, 286.031, 0], "e": [421.969, 286.031, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.97, "y": 0}, "n": "0p833_0p833_0p97_0", "t": 215, "s": [421.969, 286.031, 0], "e": [-195.031, 286.031, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 296}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [60, 60, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 30, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.9]", "cl": "9", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 0, "s": [218.531, 16.156, 0], "e": [202.969, 73.156, 0], "to": [13, 12.3125, 0], "ti": [23.5, -10.6875, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "n": "0p833_0p833_0p167_0", "t": 60, "s": [202.969, 73.156, 0], "e": [218.531, 16.156, 0], "to": [21, -9.8125, 0], "ti": [13, 22.1875, 0]}, {"t": 77, "s": [218.531, 16.156, 0], "h": 1}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 124, "s": [218.531, 16.156, 0], "e": [194.969, -13.844, 0], "to": [5.5, -8.4375, 0], "ti": [24, -2.1875, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "n": "0p833_0p833_0p167_0", "t": 165, "s": [194.969, -13.844, 0], "e": [218.531, 16.156, 0], "to": [22, -3.3125, 0], "ti": [5.75, -9.3125, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 186, "s": [218.531, 16.156, 0], "e": [218.531, 16.156, 0], "to": [13, 12.3125, 0], "ti": [-13, -12.3125, 0]}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 215, "s": [218.531, 16.156, 0], "e": [202.969, 73.156, 0], "to": [13, 12.3125, 0], "ti": [23.5, -10.6875, 0]}, {"t": 294}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.8]", "cl": "8", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [102.969, 12.594, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 3, "nm": "S<PERSON>pe Layer 1: Path 1 [1.7]", "cl": "7", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 0, "s": [55.406, -92.719, 0], "e": [121.406, -57.719, 0], "to": [44.125, -11.3125, 0], "ti": [-7.375, -18.2913818359375, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.213}, "n": "0p833_0p833_0p167_0p213", "t": 58, "s": [121.406, -57.719, 0], "e": [55.406, -92.719, 0], "to": [-19.625, -51.7086181640625, 0], "ti": [9.875, -1.1875, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 75, "s": [55.406, -92.719, 0], "e": [55.406, -92.719, 0], "to": [35.625, -1.3125, 0], "ti": [-35.625, 1.3125, 0]}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 215, "s": [55.406, -92.719, 0], "e": [121.406, -57.719, 0], "to": [44.125, -11.3125, 0], "ti": [-7.375, -18.2913818359375, 0]}, {"t": 292}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "S<PERSON>pe Layer 1: Path 1 [1.6]", "cl": "6", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.344, 16.031, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.5]", "cl": "5", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 0, "s": [-98.531, 28.719, 0], "e": [-56.531, -21.281, 0], "to": [4.5625, -32.25, 0], "ti": [-19.5625, 3.25, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.233}, "n": "0p833_0p833_0p167_0p233", "t": 57, "s": [-56.531, -21.281, 0], "e": [-98.531, 28.719, 0], "to": [-35.9375, 4.75, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 78, "s": [-98.531, 28.719, 0], "e": [-98.531, 28.719, 0], "to": [0, 0, 0], "ti": [-4.5625, 32.25, 0]}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 132, "s": [-98.531, 28.719, 0], "e": [-56.531, -21.281, 0], "to": [4.5625, -32.25, 0], "ti": [-19.5625, 3.25, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.233}, "n": "0p833_0p833_0p167_0p233", "t": 159.039, "s": [-56.531, -21.281, 0], "e": [-98.531, 28.719, 0], "to": [-35.9375, 4.75, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 169, "s": [-98.531, 28.719, 0], "e": [-98.531, 28.719, 0], "to": [0, 0, 0], "ti": [-4.5625, 32.25, 0]}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 215, "s": [-98.531, 28.719, 0], "e": [-56.531, -21.281, 0], "to": [4.5625, -32.25, 0], "ti": [-19.5625, 3.25, 0]}, {"t": 291}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.4]", "cl": "4", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-7.156, 99.531, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.3]", "cl": "3", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 0, "s": [-30.594, 212.719, 0], "e": [15.906, 233.219, 0], "to": [0.125, 15.75, 0], "ti": [-21.625, 11.25, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.222}, "n": "0p833_0p833_0p167_0p222", "t": 61, "s": [15.906, 233.219, 0], "e": [-30.594, 212.719, 0], "to": [-23.375, 12.75, 0], "ti": [-0.875, 16.25, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 81, "s": [-30.594, 212.719, 0], "e": [-30.594, 212.719, 0], "to": [0.875, -16.25, 0], "ti": [-0.125, -15.75, 0]}, {"i": {"x": 0.57, "y": 1}, "o": {"x": 0.89, "y": 0}, "n": "0p57_1_0p89_0", "t": 215, "s": [-30.594, 212.719, 0], "e": [15.906, 233.219, 0], "to": [0.125, 15.75, 0], "ti": [-21.625, 11.25, 0]}, {"t": 295}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 27, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.2]", "cl": "2", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64.969, 147.656, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.1]", "cl": "1", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.86, "y": 1}, "o": {"x": 0.605, "y": 0}, "n": "0p86_1_0p605_0", "t": 0, "s": [165.344, 204.969, 0], "e": [195.344, 163.969, 0], "to": [29.6875, 3.5, 0], "ti": [5.3125, 12, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.472, "y": 0}, "n": "0p833_0p833_0p472_0", "t": 60, "s": [195.344, 163.969, 0], "e": [165.344, 204.969, 0], "to": [9.6875, 44, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 75, "s": [165.344, 204.969, 0], "e": [165.344, 204.969, 0], "to": [0, 0, 0], "ti": [-29.6875, -3.5, 0]}, {"i": {"x": 0.86, "y": 1}, "o": {"x": 0.605, "y": 0}, "n": "0p86_1_0p605_0", "t": 215, "s": [165.344, 204.969, 0], "e": [195.344, 163.969, 0], "to": [29.6875, 3.5, 0], "ti": [5.3125, 12, 0]}, {"t": 294}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.0]", "cl": "0", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [133.031, 93.969, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 301, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [397.96, 286.023, 0], "ix": 2}, "a": {"a": 0, "k": [-2.04, -13.977, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Shape Layer 1: Path 1 [1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 14, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 13, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 12, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 11, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.4]", "np": 3, "mn": "ADBE Layer Control", "ix": 5, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.5]", "np": 3, "mn": "ADBE Layer Control", "ix": 6, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 9, "ix": 1}}]}, {"ty": 5, "nm": "S<PERSON>pe Layer 1: Path 1 [1.6]", "np": 3, "mn": "ADBE Layer Control", "ix": 7, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 8, "ix": 1}}]}, {"ty": 5, "nm": "S<PERSON>pe Layer 1: Path 1 [1.7]", "np": 3, "mn": "ADBE Layer Control", "ix": 8, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 7, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.8]", "np": 3, "mn": "ADBE Layer Control", "ix": 9, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.9]", "np": 3, "mn": "ADBE Layer Control", "ix": 10, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 5, "ix": 1}}]}], "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.446, -37.083], [3.315, -2.03], [38.496, -1.525], [3.366, 2.73], [13.346, 36.141], [-0.566, 3.77], [-30.248, 23.861], [-4.601, -0.684], [-32.04, -21.394], [-0.514, -2.787]], "o": [[-10.446, 37.083], [-3.315, 2.03], [-38.496, 1.525], [-3.366, -2.73], [-13.346, -36.141], [0.566, -3.77], [30.248, -23.861], [4.601, 0.684], [32.04, 21.394], [0.514, 2.787]], "v": [[71, 20], [103.315, 130.97], [2.919, 73.705], [-92.634, 138.73], [-69.196, 25.552], [-160.566, -45.23], [-45.685, -57.913], [-6.601, -166.684], [40.961, -61.345], [156.486, -57.787]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'Shape Layer 1: Path 1 [1.0]',\n        'Shape Layer 1: Path 1 [1.1]',\n        'Shape Layer 1: Path 1 [1.2]',\n        'Shape Layer 1: Path 1 [1.3]',\n        'Shape Layer 1: Path 1 [1.4]',\n        'Shape Layer 1: Path 1 [1.5]',\n        'Shape Layer 1: Path 1 [1.6]',\n        'Shape Layer 1: Path 1 [1.7]',\n        'Shape Layer 1: Path 1 [1.8]',\n        'Shape Layer 1: Path 1 [1.9]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0; i < nullLayerNames.length; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0; i < getNullLayers.length; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.839215686275, 0.403921568627, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 301, "st": 0, "bm": 0}], "markers": []}