import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

document.addEventListener("DOMContentLoaded", () => {
  console.log("Home.js loaded");

  const isHomePage = document.querySelector(".page.home-page");
  console.log("Home page element:", isHomePage);

  if (!isHomePage) {
    console.log("Not a home page, exiting");
    return;
  }

  gsap.registerPlugin(ScrollTrigger);
  console.log("GSAP ScrollTrigger registered");

  // Wait for Lenis to be available and integrate with ScrollTrigger
  const waitForLenis = () => {
    if (window.lenis) {
      console.log("Lenis found, integrating with ScrollTrigger");

      // Update ScrollTrigger when Lenis scrolls
      window.lenis.on('scroll', () => {
        ScrollTrigger.update();
      });

      // Refresh ScrollTrigger after Lenis is ready
      ScrollTrigger.refresh();
    } else {
      console.log("Waiting for Lenis...");
      setTimeout(waitForLenis, 100);
    }
  };

  waitForLenis();

  let scrollTriggerInstance = null;

  const initAnimations = () => {
    console.log("Initializing animations");

    if (scrollTriggerInstance) {
      scrollTriggerInstance.kill();
    }

    const trigger = document.querySelector(".hero-img-holder");
    const target = document.querySelector(".hero-img");

    console.log("Trigger element:", trigger);
    console.log("Target element:", target);

    if (!trigger || !target) {
      console.error("Required elements not found!");
      return;
    }

    scrollTriggerInstance = ScrollTrigger.create({
      trigger: ".hero-img-holder",
      start: "top bottom",
      end: "top top",
      scrub: true, // Add scrub for smoother animation
      onUpdate: (self) => {
        const progress = self.progress;
        console.log("Animation progress:", progress);
        gsap.set(".hero-img", {
          y: `${-110 + 110 * progress}%`,
          scale: 0.25 + 0.75 * progress,
        });
      },
      onToggle: (self) => {
        console.log("ScrollTrigger toggled:", self.isActive);
      }
    });

    console.log("ScrollTrigger created:", scrollTriggerInstance);
  };

  initAnimations();

  window.addEventListener("resize", () => {
    initAnimations();
  });
});
