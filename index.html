<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JavaScript Turbulent Inversion Lens Hover Effect | Codegrid</title>
    <link rel="stylesheet" href="/styles.css" />
    <link rel="stylesheet" href="/home.css" />
  </head>
  <body class="page home-page">

        <!-- home - hero -->
      <section class="hero">
        <div class="hero-header hero-header-1">
          <h1>LENS</h1>
        </div>
        <div class="hero-header hero-header-2">
          <h1>EFFECT</h1>
        </div>

        <div class="hero-footer">
          <div class="hero-footer-left">
            <p>Turbulent Inversion</p>
          </div>
          <div class="hero-footer-scroll-down">
            <p>↓ Scroll Down</p>
          </div>
          <div class="hero-footer-right">
            <div class="hero-footer-symbols">★</div>
          </div>
        </div>
      </section>

      <!-- home - hero img holder -->
      <section class="hero-img-holder">
        <div class="hero-img">
          <img src="/portrait.jpeg" alt="" />
        </div>
      </section>

  
    

    <div class="spacer">
        <div class="inversion-lens">
      <img src="./portrait.jpeg" alt="" />
    </div>
    </div>
    <script type="module" src="/script.js"></script>
    <script type="module" src="/js/home.js"></script>
    <script type="module" src="/js/smoothscroll.js"></script>
  </body>
</html>
