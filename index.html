<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JavaScript Turbulent Inversion Lens Hover Effect | Codegrid</title>
    <link rel="stylesheet" href="/styles.css" />
    <link rel="stylesheet" href="/home.css" />
  </head>
  <body>

        <!-- home - hero -->
      <section class="hero">
    
      </section>

      <!-- home - hero img holder -->
      <section class="hero-img-holder">
        <div class="hero-img">
          <img src="/portrait.jpeg" alt="" />
        </div>
      </section>

  
    

    <div class="spacer">
        <div class="inversion-lens">
      <img src="./portrait.jpeg" alt="" />
    </div>
    </div>
    <script type="module" src="/script.js"></script>
    <script type="module" src="/js/home.js"></script>
  </body>
</html>
