/* eslint-disable */
/*! howler.js v2.2.0 | (c) 2013-2020, <PERSON> of GoldFire Studios | MIT License | howlerjs.com */
!function () { 'use strict'; var e = function () { this.init(); }; e.prototype = { init: function () { var e = this || n; return e._counter = 1e3, e._html5AudioPool = [], e.html5PoolSize = 10, e._codecs = {}, e._howls = [], e._muted = !1, e._volume = 1, e._canPlayEvent = 'canplaythrough', e._navigator = typeof window != 'undefined' && window.navigator ? window.navigator : null, e.masterGain = null, e.noAudio = !1, e.usingWebAudio = !0, e.autoSuspend = !0, e.ctx = null, e.autoUnlock = !0, e._setup(), e; }, volume: function (e) { var o = this || n; if (e = parseFloat(e), o.ctx || _(), void 0 !== e && e >= 0 && e <= 1) { if (o._volume = e, o._muted) return o; o.usingWebAudio && o.masterGain.gain.setValueAtTime(e, n.ctx.currentTime); for (var t = 0; t < o._howls.length; t++) if (!o._howls[t]._webAudio) for (var r = o._howls[t]._getSoundIds(), a = 0; a < r.length; a++) { var u = o._howls[t]._soundById(r[a]); u && u._node && (u._node.volume = u._volume * e); } return o; } return o._volume; }, mute: function (e) { var o = this || n; o.ctx || _(), o._muted = e, o.usingWebAudio && o.masterGain.gain.setValueAtTime(e ? 0 : o._volume, n.ctx.currentTime); for (var t = 0; t < o._howls.length; t++) if (!o._howls[t]._webAudio) for (var r = o._howls[t]._getSoundIds(), a = 0; a < r.length; a++) { var u = o._howls[t]._soundById(r[a]); u && u._node && (u._node.muted = !!e || u._muted); } return o; }, stop: function () { for (var e = this || n, o = 0; o < e._howls.length; o++)e._howls[o].stop(); return e; }, unload: function () { for (var e = this || n, o = e._howls.length - 1; o >= 0; o--)e._howls[o].unload(); return e.usingWebAudio && e.ctx && void 0 !== e.ctx.close && (e.ctx.close(), e.ctx = null, _()), e; }, codecs: function (e) { return (this || n)._codecs[e.replace(/^x-/, '')]; }, _setup: function () { var e = this || n; if (e.state = e.ctx ? e.ctx.state || 'suspended' : 'suspended', e._autoSuspend(), !e.usingWebAudio) if (typeof Audio != 'undefined') try { var o = new Audio; void 0 === o.oncanplaythrough && (e._canPlayEvent = 'canplay'); } catch (n) { e.noAudio = !0; } else e.noAudio = !0; try { var o = new Audio; o.muted && (e.noAudio = !0); } catch (e) {} return e.noAudio || e._setupCodecs(), e; }, _setupCodecs: function () { var e = this || n, o = null; try { o = typeof Audio != 'undefined' ? new Audio : null; } catch (n) { return e; } if (!o || typeof o.canPlayType != 'function') return e; var t = o.canPlayType('audio/mpeg;').replace(/^no$/, ''), r = e._navigator && e._navigator.userAgent.match(/OPR\/([0-6].)/g), a = r && parseInt(r[0].split('/')[1], 10) < 33; return e._codecs = { mp3: !(a || !t && !o.canPlayType('audio/mp3;').replace(/^no$/, '')), mpeg: !!t, opus: !!o.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/, ''), ogg: !!o.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''), oga: !!o.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''), wav: !!o.canPlayType('audio/wav; codecs="1"').replace(/^no$/, ''), aac: !!o.canPlayType('audio/aac;').replace(/^no$/, ''), caf: !!o.canPlayType('audio/x-caf;').replace(/^no$/, ''), m4a: !!(o.canPlayType('audio/x-m4a;') || o.canPlayType('audio/m4a;') || o.canPlayType('audio/aac;')).replace(/^no$/, ''), m4b: !!(o.canPlayType('audio/x-m4b;') || o.canPlayType('audio/m4b;') || o.canPlayType('audio/aac;')).replace(/^no$/, ''), mp4: !!(o.canPlayType('audio/x-mp4;') || o.canPlayType('audio/mp4;') || o.canPlayType('audio/aac;')).replace(/^no$/, ''), weba: !!o.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, ''), webm: !!o.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, ''), dolby: !!o.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/, ''), flac: !!(o.canPlayType('audio/x-flac;') || o.canPlayType('audio/flac;')).replace(/^no$/, '') }, e; }, _unlockAudio: function () { var e = this || n; if (!e._audioUnlocked && e.ctx) { e._audioUnlocked = !1, e.autoUnlock = !1, e._mobileUnloaded || e.ctx.sampleRate === 44100 || (e._mobileUnloaded = !0, e.unload()), e._scratchBuffer = e.ctx.createBuffer(1, 1, 22050); var o = function (n) { for (;e._html5AudioPool.length < e.html5PoolSize;) try { var t = new Audio; t._unlocked = !0, e._releaseHtml5Audio(t); } catch (n) { e.noAudio = !0; break; } for (var r = 0; r < e._howls.length; r++) if (!e._howls[r]._webAudio) for (var a = e._howls[r]._getSoundIds(), u = 0; u < a.length; u++) { var i = e._howls[r]._soundById(a[u]); i && i._node && !i._node._unlocked && (i._node._unlocked = !0, i._node.load()); }e._autoResume(); var d = e.ctx.createBufferSource(); d.buffer = e._scratchBuffer, d.connect(e.ctx.destination), void 0 === d.start ? d.noteOn(0) : d.start(0), typeof e.ctx.resume == 'function' && e.ctx.resume(), d.onended = function () { d.disconnect(0), e._audioUnlocked = !0, document.removeEventListener('touchstart', o, !0), document.removeEventListener('touchend', o, !0), document.removeEventListener('click', o, !0); for (var n = 0; n < e._howls.length; n++)e._howls[n]._emit('unlock'); }; }; return document.addEventListener('touchstart', o, !0), document.addEventListener('touchend', o, !0), document.addEventListener('click', o, !0), e; } }, _obtainHtml5Audio: function () { var e = this || n; if (e._html5AudioPool.length) return e._html5AudioPool.pop(); var o = (new Audio).play(); return o && typeof Promise != 'undefined' && (o instanceof Promise || typeof o.then == 'function') && o.catch(function () { console.warn('HTML5 Audio pool exhausted, returning potentially locked audio object.'); }), new Audio; }, _releaseHtml5Audio: function (e) { var o = this || n; return e._unlocked && o._html5AudioPool.push(e), o; }, _autoSuspend: function () { var e = this; if (e.autoSuspend && e.ctx && void 0 !== e.ctx.suspend && n.usingWebAudio) { for (var o = 0; o < e._howls.length; o++) if (e._howls[o]._webAudio) for (var t = 0; t < e._howls[o]._sounds.length; t++) if (!e._howls[o]._sounds[t]._paused) return e; return e._suspendTimer && clearTimeout(e._suspendTimer), e._suspendTimer = setTimeout(function () { if (e.autoSuspend) { e._suspendTimer = null, e.state = 'suspending'; var n = function () { e.state = 'suspended', e._resumeAfterSuspend && (delete e._resumeAfterSuspend, e._autoResume()); }; e.ctx.suspend().then(n, n); } }, 3e4), e; } }, _autoResume: function () { var e = this; if (e.ctx && void 0 !== e.ctx.resume && n.usingWebAudio) return e.state === 'running' && e.ctx.state !== 'interrupted' && e._suspendTimer ? (clearTimeout(e._suspendTimer), e._suspendTimer = null) : e.state === 'suspended' || e.state === 'running' && e.ctx.state === 'interrupted' ? (e.ctx.resume().then(function () { e.state = 'running'; for (var n = 0; n < e._howls.length; n++)e._howls[n]._emit('resume'); }), e._suspendTimer && (clearTimeout(e._suspendTimer), e._suspendTimer = null)) : e.state === 'suspending' && (e._resumeAfterSuspend = !0), e; } }; var n = new e, o = function (e) { var n = this; if (!e.src || e.src.length === 0) return void console.error('An array of source files must be passed with any new Howl.'); n.init(e); }; o.prototype = { init: function (e) { var o = this; return n.ctx || _(), o._autoplay = e.autoplay || !1, o._format = typeof e.format != 'string' ? e.format : [e.format], o._html5 = e.html5 || !1, o._muted = e.mute || !1, o._loop = e.loop || !1, o._pool = e.pool || 5, o._preload = typeof e.preload != 'boolean' && e.preload !== 'metadata' || e.preload, o._rate = e.rate || 1, o._sprite = e.sprite || {}, o._src = typeof e.src != 'string' ? e.src : [e.src], o._volume = void 0 !== e.volume ? e.volume : 1, o._xhr = { method: e.xhr && e.xhr.method ? e.xhr.method : 'GET', headers: e.xhr && e.xhr.headers ? e.xhr.headers : null, withCredentials: !(!e.xhr || !e.xhr.withCredentials) && e.xhr.withCredentials }, o._duration = 0, o._state = 'unloaded', o._sounds = [], o._endTimers = {}, o._queue = [], o._playLock = !1, o._onend = e.onend ? [{ fn: e.onend }] : [], o._onfade = e.onfade ? [{ fn: e.onfade }] : [], o._onload = e.onload ? [{ fn: e.onload }] : [], o._onloaderror = e.onloaderror ? [{ fn: e.onloaderror }] : [], o._onplayerror = e.onplayerror ? [{ fn: e.onplayerror }] : [], o._onpause = e.onpause ? [{ fn: e.onpause }] : [], o._onplay = e.onplay ? [{ fn: e.onplay }] : [], o._onstop = e.onstop ? [{ fn: e.onstop }] : [], o._onmute = e.onmute ? [{ fn: e.onmute }] : [], o._onvolume = e.onvolume ? [{ fn: e.onvolume }] : [], o._onrate = e.onrate ? [{ fn: e.onrate }] : [], o._onseek = e.onseek ? [{ fn: e.onseek }] : [], o._onunlock = e.onunlock ? [{ fn: e.onunlock }] : [], o._onresume = [], o._webAudio = n.usingWebAudio && !o._html5, void 0 !== n.ctx && n.ctx && n.autoUnlock && n._unlockAudio(), n._howls.push(o), o._autoplay && o._queue.push({ event: 'play', action: function () { o.play(); } }), o._preload && o._preload !== 'none' && o.load(), o; }, load: function () { var e = this, o = null; if (n.noAudio) return void e._emit('loaderror', null, 'No audio support.'); typeof e._src == 'string' && (e._src = [e._src]); for (var r = 0; r < e._src.length; r++) { var u, i; if (e._format && e._format[r])u = e._format[r]; else { if (typeof (i = e._src[r]) != 'string') { e._emit('loaderror', null, 'Non-string found in selected audio sources - ignoring.'); continue; }u = /^data:audio\/([^;,]+);/i.exec(i), u || (u = /\.([^.]+)$/.exec(i.split('?', 1)[0])), u && (u = u[1].toLowerCase()); } if (u || console.warn('No file extension was found. Consider using the "format" property or specify an extension.'), u && n.codecs(u)) { o = e._src[r]; break; } } return o ? (e._src = o, e._state = 'loading', window.location.protocol === 'https:' && o.slice(0, 5) === 'http:' && (e._html5 = !0, e._webAudio = !1), new t(e), e._webAudio && a(e), e) : void e._emit('loaderror', null, 'No codec support for selected audio sources.'); }, play: function (e, o) { var t = this, r = null; if (typeof e == 'number')r = e, e = null; else { if (typeof e == 'string' && t._state === 'loaded' && !t._sprite[e]) return null; if (void 0 === e && (e = '__default', !t._playLock)) { for (var a = 0, u = 0; u < t._sounds.length; u++)t._sounds[u]._paused && !t._sounds[u]._ended && (a++, r = t._sounds[u]._id); a === 1 ? e = null : r = null; } } var i = r ? t._soundById(r) : t._inactiveSound(); if (!i) return null; if (r && !e && (e = i._sprite || '__default'), t._state !== 'loaded') { i._sprite = e, i._ended = !1; var d = i._id; return t._queue.push({ event: 'play', action: function () { t.play(d); } }), d; } if (r && !i._paused) return o || t._loadQueue('play'), i._id; t._webAudio && n._autoResume(); var _ = Math.max(0, i._seek > 0 ? i._seek : t._sprite[e][0] / 1e3), s = Math.max(0, (t._sprite[e][0] + t._sprite[e][1]) / 1e3 - _), l = 1e3 * s / Math.abs(i._rate), c = t._sprite[e][0] / 1e3, f = (t._sprite[e][0] + t._sprite[e][1]) / 1e3; i._sprite = e, i._ended = !1; var p = function () { i._paused = !1, i._seek = _, i._start = c, i._stop = f, i._loop = !(!i._loop && !t._sprite[e][2]); }; if (_ >= f) return void t._ended(i); var m = i._node; if (t._webAudio) { var v = function () { t._playLock = !1, p(), t._refreshBuffer(i); var e = i._muted || t._muted ? 0 : i._volume; m.gain.setValueAtTime(e, n.ctx.currentTime), i._playStart = n.ctx.currentTime, void 0 === m.bufferSource.start ? i._loop ? m.bufferSource.noteGrainOn(0, _, 86400) : m.bufferSource.noteGrainOn(0, _, s) : i._loop ? m.bufferSource.start(0, _, 86400) : m.bufferSource.start(0, _, s), l !== 1 / 0 && (t._endTimers[i._id] = setTimeout(t._ended.bind(t, i), l)), o || setTimeout(function () { t._emit('play', i._id), t._loadQueue(); }, 0); }; n.state === 'running' && n.ctx.state !== 'interrupted' ? v() : (t._playLock = !0, t.once('resume', v), t._clearTimer(i._id)); } else { var h = function () { m.currentTime = _, m.muted = i._muted || t._muted || n._muted || m.muted, m.volume = i._volume * n.volume(), m.playbackRate = i._rate; try { var r = m.play(); if (r && typeof Promise != 'undefined' && (r instanceof Promise || typeof r.then == 'function') ? (t._playLock = !0, p(), r.then(function () { t._playLock = !1, m._unlocked = !0, o || (t._emit('play', i._id), t._loadQueue()); }).catch(function () { t._playLock = !1, t._emit('playerror', i._id, 'Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.'), i._ended = !0, i._paused = !0; })) : o || (t._playLock = !1, p(), t._emit('play', i._id), t._loadQueue()), m.playbackRate = i._rate, m.paused) return void t._emit('playerror', i._id, 'Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.'); e !== '__default' || i._loop ? t._endTimers[i._id] = setTimeout(t._ended.bind(t, i), l) : (t._endTimers[i._id] = function () { t._ended(i), m.removeEventListener('ended', t._endTimers[i._id], !1); }, m.addEventListener('ended', t._endTimers[i._id], !1)); } catch (e) { t._emit('playerror', i._id, e); } }; m.src === 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA' && (m.src = t._src, m.load()); var y = window && window.ejecta || !m.readyState && n._navigator.isCocoonJS; if (m.readyState >= 3 || y)h(); else { t._playLock = !0; var g = function () { h(), m.removeEventListener(n._canPlayEvent, g, !1); }; m.addEventListener(n._canPlayEvent, g, !1), t._clearTimer(i._id); } } return i._id; }, pause: function (e) { var n = this; if (n._state !== 'loaded' || n._playLock) return n._queue.push({ event: 'pause', action: function () { n.pause(e); } }), n; for (var o = n._getSoundIds(e), t = 0; t < o.length; t++) { n._clearTimer(o[t]); var r = n._soundById(o[t]); if (r && !r._paused && (r._seek = n.seek(o[t]), r._rateSeek = 0, r._paused = !0, n._stopFade(o[t]), r._node)) if (n._webAudio) { if (!r._node.bufferSource) continue; void 0 === r._node.bufferSource.stop ? r._node.bufferSource.noteOff(0) : r._node.bufferSource.stop(0), n._cleanBuffer(r._node); } else isNaN(r._node.duration) && r._node.duration !== 1 / 0 || r._node.pause(); arguments[1] || n._emit('pause', r ? r._id : null); } return n; }, stop: function (e, n) { var o = this; if (o._state !== 'loaded' || o._playLock) return o._queue.push({ event: 'stop', action: function () { o.stop(e); } }), o; for (var t = o._getSoundIds(e), r = 0; r < t.length; r++) { o._clearTimer(t[r]); var a = o._soundById(t[r]); a && (a._seek = a._start || 0, a._rateSeek = 0, a._paused = !0, a._ended = !0, o._stopFade(t[r]), a._node && (o._webAudio ? a._node.bufferSource && (void 0 === a._node.bufferSource.stop ? a._node.bufferSource.noteOff(0) : a._node.bufferSource.stop(0), o._cleanBuffer(a._node)) : isNaN(a._node.duration) && a._node.duration !== 1 / 0 || (a._node.currentTime = a._start || 0, a._node.pause(), a._node.duration === 1 / 0 && o._clearSound(a._node))), n || o._emit('stop', a._id)); } return o; }, mute: function (e, o) { var t = this; if (t._state !== 'loaded' || t._playLock) return t._queue.push({ event: 'mute', action: function () { t.mute(e, o); } }), t; if (void 0 === o) { if (typeof e != 'boolean') return t._muted; t._muted = e; } for (var r = t._getSoundIds(o), a = 0; a < r.length; a++) { var u = t._soundById(r[a]); u && (u._muted = e, u._interval && t._stopFade(u._id), t._webAudio && u._node ? u._node.gain.setValueAtTime(e ? 0 : u._volume, n.ctx.currentTime) : u._node && (u._node.muted = !!n._muted || e), t._emit('mute', u._id)); } return t; }, volume: function () { var e, o, t = this, r = arguments; if (r.length === 0) return t._volume; if (r.length === 1 || r.length === 2 && void 0 === r[1]) { t._getSoundIds().indexOf(r[0]) >= 0 ? o = parseInt(r[0], 10) : e = parseFloat(r[0]); } else r.length >= 2 && (e = parseFloat(r[0]), o = parseInt(r[1], 10)); var a; if (!(void 0 !== e && e >= 0 && e <= 1)) return a = o ? t._soundById(o) : t._sounds[0], a ? a._volume : 0; if (t._state !== 'loaded' || t._playLock) return t._queue.push({ event: 'volume', action: function () { t.volume.apply(t, r); } }), t; void 0 === o && (t._volume = e), o = t._getSoundIds(o); for (var u = 0; u < o.length; u++)(a = t._soundById(o[u])) && (a._volume = e, r[2] || t._stopFade(o[u]), t._webAudio && a._node && !a._muted ? a._node.gain.setValueAtTime(e, n.ctx.currentTime) : a._node && !a._muted && (a._node.volume = e * n.volume()), t._emit('volume', a._id)); return t; }, fade: function (e, o, t, r) { var a = this; if (a._state !== 'loaded' || a._playLock) return a._queue.push({ event: 'fade', action: function () { a.fade(e, o, t, r); } }), a; e = Math.min(Math.max(0, parseFloat(e)), 1), o = Math.min(Math.max(0, parseFloat(o)), 1), t = parseFloat(t), a.volume(e, r); for (var u = a._getSoundIds(r), i = 0; i < u.length; i++) { var d = a._soundById(u[i]); if (d) { if (r || a._stopFade(u[i]), a._webAudio && !d._muted) { var _ = n.ctx.currentTime, s = _ + t / 1e3; d._volume = e, d._node.gain.setValueAtTime(e, _), d._node.gain.linearRampToValueAtTime(o, s); }a._startFadeInterval(d, e, o, t, u[i], void 0 === r); } } return a; }, _startFadeInterval: function (e, n, o, t, r, a) { var u = this, i = n, d = o - n, _ = Math.abs(d / .01), s = Math.max(4, _ > 0 ? t / _ : t), l = Date.now(); e._fadeTo = o, e._interval = setInterval(function () { var r = (Date.now() - l) / t; l = Date.now(), i += d * r, i = d < 0 ? Math.max(o, i) : Math.min(o, i), i = Math.round(100 * i) / 100, u._webAudio ? e._volume = i : u.volume(i, e._id, !0), a && (u._volume = i), (o < n && i <= o || o > n && i >= o) && (clearInterval(e._interval), e._interval = null, e._fadeTo = null, u.volume(o, e._id), u._emit('fade', e._id)); }, s); }, _stopFade: function (e) { var o = this, t = o._soundById(e); return t && t._interval && (o._webAudio && t._node.gain.cancelScheduledValues(n.ctx.currentTime), clearInterval(t._interval), t._interval = null, o.volume(t._fadeTo, e), t._fadeTo = null, o._emit('fade', e)), o; }, loop: function () { var e, n, o, t = this, r = arguments; if (r.length === 0) return t._loop; if (r.length === 1) { if (typeof r[0] != 'boolean') return !!(o = t._soundById(parseInt(r[0], 10))) && o._loop; e = r[0], t._loop = e; } else r.length === 2 && (e = r[0], n = parseInt(r[1], 10)); for (var a = t._getSoundIds(n), u = 0; u < a.length; u++)(o = t._soundById(a[u])) && (o._loop = e, t._webAudio && o._node && o._node.bufferSource && (o._node.bufferSource.loop = e, e && (o._node.bufferSource.loopStart = o._start || 0, o._node.bufferSource.loopEnd = o._stop))); return t; }, rate: function () { var e, o, t = this, r = arguments; if (r.length === 0)o = t._sounds[0]._id; else if (r.length === 1) { var a = t._getSoundIds(), u = a.indexOf(r[0]); u >= 0 ? o = parseInt(r[0], 10) : e = parseFloat(r[0]); } else r.length === 2 && (e = parseFloat(r[0]), o = parseInt(r[1], 10)); var i; if (typeof e != 'number') return i = t._soundById(o), i ? i._rate : t._rate; if (t._state !== 'loaded' || t._playLock) return t._queue.push({ event: 'rate', action: function () { t.rate.apply(t, r); } }), t; void 0 === o && (t._rate = e), o = t._getSoundIds(o); for (var d = 0; d < o.length; d++) if (i = t._soundById(o[d])) { t.playing(o[d]) && (i._rateSeek = t.seek(o[d]), i._playStart = t._webAudio ? n.ctx.currentTime : i._playStart), i._rate = e, t._webAudio && i._node && i._node.bufferSource ? i._node.bufferSource.playbackRate.setValueAtTime(e, n.ctx.currentTime) : i._node && (i._node.playbackRate = e); var _ = t.seek(o[d]), s = (t._sprite[i._sprite][0] + t._sprite[i._sprite][1]) / 1e3 - _, l = 1e3 * s / Math.abs(i._rate); !t._endTimers[o[d]] && i._paused || (t._clearTimer(o[d]), t._endTimers[o[d]] = setTimeout(t._ended.bind(t, i), l)), t._emit('rate', i._id); } return t; }, seek: function () { var e, o, t = this, r = arguments; if (r.length === 0)o = t._sounds[0]._id; else if (r.length === 1) { var a = t._getSoundIds(), u = a.indexOf(r[0]); u >= 0 ? o = parseInt(r[0], 10) : t._sounds.length && (o = t._sounds[0]._id, e = parseFloat(r[0])); } else r.length === 2 && (e = parseFloat(r[0]), o = parseInt(r[1], 10)); if (void 0 === o) return t; if (t._state !== 'loaded' || t._playLock) return t._queue.push({ event: 'seek', action: function () { t.seek.apply(t, r); } }), t; var i = t._soundById(o); if (i) { if (!(typeof e == 'number' && e >= 0)) { if (t._webAudio) { var d = t.playing(o) ? n.ctx.currentTime - i._playStart : 0, _ = i._rateSeek ? i._rateSeek - i._seek : 0; return i._seek + (_ + d * Math.abs(i._rate)); } return i._node.currentTime; } var s = t.playing(o); s && t.pause(o, !0), i._seek = e, i._ended = !1, t._clearTimer(o), t._webAudio || !i._node || isNaN(i._node.duration) || (i._node.currentTime = e); var l = function () { t._emit('seek', o), s && t.play(o, !0); }; if (s && !t._webAudio) { var c = function () { t._playLock ? setTimeout(c, 0) : l(); }; setTimeout(c, 0); } else l(); } return t; }, playing: function (e) { var n = this; if (typeof e == 'number') { var o = n._soundById(e); return !!o && !o._paused; } for (var t = 0; t < n._sounds.length; t++) if (!n._sounds[t]._paused) return !0; return !1; }, duration: function (e) { var n = this, o = n._duration, t = n._soundById(e); return t && (o = n._sprite[t._sprite][1] / 1e3), o; }, state: function () { return this._state; }, unload: function () { for (var e = this, o = e._sounds, t = 0; t < o.length; t++)o[t]._paused || e.stop(o[t]._id), e._webAudio || (e._clearSound(o[t]._node), o[t]._node.removeEventListener('error', o[t]._errorFn, !1), o[t]._node.removeEventListener(n._canPlayEvent, o[t]._loadFn, !1), n._releaseHtml5Audio(o[t]._node)), delete o[t]._node, e._clearTimer(o[t]._id); var a = n._howls.indexOf(e); a >= 0 && n._howls.splice(a, 1); var u = !0; for (t = 0; t < n._howls.length; t++) if (n._howls[t]._src === e._src || e._src.indexOf(n._howls[t]._src) >= 0) { u = !1; break; } return r && u && delete r[e._src], n.noAudio = !1, e._state = 'unloaded', e._sounds = [], e = null, null; }, on: function (e, n, o, t) { var r = this, a = r['_on' + e]; return typeof n == 'function' && a.push(t ? { id: o, fn: n, once: t } : { id: o, fn: n }), r; }, off: function (e, n, o) { var t = this, r = t['_on' + e], a = 0; if (typeof n == 'number' && (o = n, n = null), n || o) for (a = 0; a < r.length; a++) { var u = o === r[a].id; if (n === r[a].fn && u || !n && u) { r.splice(a, 1); break; } } else if (e)t['_on' + e] = []; else { var i = Object.keys(t); for (a = 0; a < i.length; a++)i[a].indexOf('_on') === 0 && Array.isArray(t[i[a]]) && (t[i[a]] = []); } return t; }, once: function (e, n, o) { var t = this; return t.on(e, n, o, 1), t; }, _emit: function (e, n, o) { for (var t = this, r = t['_on' + e], a = r.length - 1; a >= 0; a--)r[a].id && r[a].id !== n && e !== 'load' || (setTimeout(function (e) { e.call(this, n, o); }.bind(t, r[a].fn), 0), r[a].once && t.off(e, r[a].fn, r[a].id)); return t._loadQueue(e), t; }, _loadQueue: function (e) { var n = this; if (n._queue.length > 0) { var o = n._queue[0]; o.event === e && (n._queue.shift(), n._loadQueue()), e || o.action(); } return n; }, _ended: function (e) { var o = this, t = e._sprite; if (!o._webAudio && e._node && !e._node.paused && !e._node.ended && e._node.currentTime < e._stop) return setTimeout(o._ended.bind(o, e), 100), o; var r = !(!e._loop && !o._sprite[t][2]); if (o._emit('end', e._id), !o._webAudio && r && o.stop(e._id, !0).play(e._id), o._webAudio && r) { o._emit('play', e._id), e._seek = e._start || 0, e._rateSeek = 0, e._playStart = n.ctx.currentTime; var a = 1e3 * (e._stop - e._start) / Math.abs(e._rate); o._endTimers[e._id] = setTimeout(o._ended.bind(o, e), a); } return o._webAudio && !r && (e._paused = !0, e._ended = !0, e._seek = e._start || 0, e._rateSeek = 0, o._clearTimer(e._id), o._cleanBuffer(e._node), n._autoSuspend()), o._webAudio || r || o.stop(e._id, !0), o; }, _clearTimer: function (e) { var n = this; if (n._endTimers[e]) { if (typeof n._endTimers[e] != 'function')clearTimeout(n._endTimers[e]); else { var o = n._soundById(e); o && o._node && o._node.removeEventListener('ended', n._endTimers[e], !1); } delete n._endTimers[e]; } return n; }, _soundById: function (e) { for (var n = this, o = 0; o < n._sounds.length; o++) if (e === n._sounds[o]._id) return n._sounds[o]; return null; }, _inactiveSound: function () { var e = this; e._drain(); for (var n = 0; n < e._sounds.length; n++) if (e._sounds[n]._ended) return e._sounds[n].reset(); return new t(e); }, _drain: function () { var e = this, n = e._pool, o = 0, t = 0; if (!(e._sounds.length < n)) { for (t = 0; t < e._sounds.length; t++)e._sounds[t]._ended && o++; for (t = e._sounds.length - 1; t >= 0; t--) { if (o <= n) return; e._sounds[t]._ended && (e._webAudio && e._sounds[t]._node && e._sounds[t]._node.disconnect(0), e._sounds.splice(t, 1), o--); } } }, _getSoundIds: function (e) { var n = this; if (void 0 === e) { for (var o = [], t = 0; t < n._sounds.length; t++)o.push(n._sounds[t]._id); return o; } return [e]; }, _refreshBuffer: function (e) { var o = this; return e._node.bufferSource = n.ctx.createBufferSource(), e._node.bufferSource.buffer = r[o._src], e._panner ? e._node.bufferSource.connect(e._panner) : e._node.bufferSource.connect(e._node), e._node.bufferSource.loop = e._loop, e._loop && (e._node.bufferSource.loopStart = e._start || 0, e._node.bufferSource.loopEnd = e._stop || 0), e._node.bufferSource.playbackRate.setValueAtTime(e._rate, n.ctx.currentTime), o; }, _cleanBuffer: function (e) { var o = this, t = n._navigator && n._navigator.vendor.indexOf('Apple') >= 0; if (n._scratchBuffer && e.bufferSource && (e.bufferSource.onended = null, e.bufferSource.disconnect(0), t)) try { e.bufferSource.buffer = n._scratchBuffer; } catch (e) {} return e.bufferSource = null, o; }, _clearSound: function (e) { /MSIE |Trident\//.test(n._navigator && n._navigator.userAgent) || (e.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA'); } }; var t = function (e) { this._parent = e, this.init(); }; t.prototype = { init: function () { var e = this, o = e._parent; return e._muted = o._muted, e._loop = o._loop, e._volume = o._volume, e._rate = o._rate, e._seek = 0, e._paused = !0, e._ended = !0, e._sprite = '__default', e._id = ++n._counter, o._sounds.push(e), e.create(), e; }, create: function () { var e = this, o = e._parent, t = n._muted || e._muted || e._parent._muted ? 0 : e._volume; return o._webAudio ? (e._node = void 0 === n.ctx.createGain ? n.ctx.createGainNode() : n.ctx.createGain(), e._node.gain.setValueAtTime(t, n.ctx.currentTime), e._node.paused = !0, e._node.connect(n.masterGain)) : n.noAudio || (e._node = n._obtainHtml5Audio(), e._errorFn = e._errorListener.bind(e), e._node.addEventListener('error', e._errorFn, !1), e._loadFn = e._loadListener.bind(e), e._node.addEventListener(n._canPlayEvent, e._loadFn, !1), e._node.src = o._src, e._node.preload = !0 === o._preload ? 'auto' : o._preload, e._node.volume = t * n.volume(), e._node.load()), e; }, reset: function () { var e = this, o = e._parent; return e._muted = o._muted, e._loop = o._loop, e._volume = o._volume, e._rate = o._rate, e._seek = 0, e._rateSeek = 0, e._paused = !0, e._ended = !0, e._sprite = '__default', e._id = ++n._counter, e; }, _errorListener: function () { var e = this; e._parent._emit('loaderror', e._id, e._node.error ? e._node.error.code : 0), e._node.removeEventListener('error', e._errorFn, !1); }, _loadListener: function () { var e = this, o = e._parent; o._duration = Math.ceil(10 * e._node.duration) / 10, Object.keys(o._sprite).length === 0 && (o._sprite = { __default: [0, 1e3 * o._duration] }), o._state !== 'loaded' && (o._state = 'loaded', o._emit('load'), o._loadQueue()), e._node.removeEventListener(n._canPlayEvent, e._loadFn, !1); } }; var r = {}, a = function (e) { var n = e._src; if (r[n]) return e._duration = r[n].duration, void d(e); if (/^data:[^;]+;base64,/.test(n)) { for (var o = atob(n.split(',')[1]), t = new Uint8Array(o.length), a = 0; a < o.length; ++a)t[a] = o.charCodeAt(a); i(t.buffer, e); } else { var _ = new XMLHttpRequest; _.open(e._xhr.method, n, !0), _.withCredentials = e._xhr.withCredentials, _.responseType = 'arraybuffer', e._xhr.headers && Object.keys(e._xhr.headers).forEach(function (n) { _.setRequestHeader(n, e._xhr.headers[n]); }), _.onload = function () { var n = (_.status + '')[0]; if (n !== '0' && n !== '2' && n !== '3') return void e._emit('loaderror', null, 'Failed loading audio file with status: ' + _.status + '.'); i(_.response, e); }, _.onerror = function () { e._webAudio && (e._html5 = !0, e._webAudio = !1, e._sounds = [], delete r[n], e.load()); }, u(_); } }, u = function (e) { try { e.send(); } catch (n) { e.onerror(); } }, i = function (e, o) { var t = function () { o._emit('loaderror', null, 'Decoding audio data failed.'); }, a = function (e) { e && o._sounds.length > 0 ? (r[o._src] = e, d(o, e)) : t(); }; typeof Promise != 'undefined' && n.ctx.decodeAudioData.length === 1 ? n.ctx.decodeAudioData(e).then(a).catch(t) : n.ctx.decodeAudioData(e, a, t); }, d = function (e, n) { n && !e._duration && (e._duration = n.duration), Object.keys(e._sprite).length === 0 && (e._sprite = { __default: [0, 1e3 * e._duration] }), e._state !== 'loaded' && (e._state = 'loaded', e._emit('load'), e._loadQueue()); }, _ = function () { if (n.usingWebAudio) { try { typeof AudioContext != 'undefined' ? n.ctx = new AudioContext : typeof webkitAudioContext != 'undefined' ? n.ctx = new webkitAudioContext : n.usingWebAudio = !1; } catch (e) { n.usingWebAudio = !1; }n.ctx || (n.usingWebAudio = !1); var e = /iP(hone|od|ad)/.test(n._navigator && n._navigator.platform), o = n._navigator && n._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/), t = o ? parseInt(o[1], 10) : null; if (e && t && t < 9) { var r = /safari/.test(n._navigator && n._navigator.userAgent.toLowerCase()); n._navigator && !r && (n.usingWebAudio = !1); }n.usingWebAudio && (n.masterGain = void 0 === n.ctx.createGain ? n.ctx.createGainNode() : n.ctx.createGain(), n.masterGain.gain.setValueAtTime(n._muted ? 0 : n._volume, n.ctx.currentTime), n.masterGain.connect(n.ctx.destination)), n._setup(); } }; typeof define == 'function' && define.amd && define([], function () { return { Howler: n, Howl: o }; }), typeof exports != 'undefined' && (exports.Howler = n, exports.Howl = o), typeof global != 'undefined' ? (global.HowlerGlobal = e, global.Howler = n, global.Howl = o, global.Sound = t) : typeof window != 'undefined' && (window.HowlerGlobal = e, window.Howler = n, window.Howl = o, window.Sound = t); }();
/*! Spatial Plugin */
!function () { 'use strict'; HowlerGlobal.prototype._pos = [0, 0, 0], HowlerGlobal.prototype._orientation = [0, 0, -1, 0, 1, 0], HowlerGlobal.prototype.stereo = function (e) { var n = this; if (!n.ctx || !n.ctx.listener) return n; for (var t = n._howls.length - 1; t >= 0; t--)n._howls[t].stereo(e); return n; }, HowlerGlobal.prototype.pos = function (e, n, t) { var r = this; return r.ctx && r.ctx.listener ? (n = typeof n != 'number' ? r._pos[1] : n, t = typeof t != 'number' ? r._pos[2] : t, typeof e != 'number' ? r._pos : (r._pos = [e, n, t], void 0 !== r.ctx.listener.positionX ? (r.ctx.listener.positionX.setTargetAtTime(r._pos[0], Howler.ctx.currentTime, .1), r.ctx.listener.positionY.setTargetAtTime(r._pos[1], Howler.ctx.currentTime, .1), r.ctx.listener.positionZ.setTargetAtTime(r._pos[2], Howler.ctx.currentTime, .1)) : r.ctx.listener.setPosition(r._pos[0], r._pos[1], r._pos[2]), r)) : r; }, HowlerGlobal.prototype.orientation = function (e, n, t, r, o, i) { var a = this; if (!a.ctx || !a.ctx.listener) return a; var s = a._orientation; return n = typeof n != 'number' ? s[1] : n, t = typeof t != 'number' ? s[2] : t, r = typeof r != 'number' ? s[3] : r, o = typeof o != 'number' ? s[4] : o, i = typeof i != 'number' ? s[5] : i, typeof e != 'number' ? s : (a._orientation = [e, n, t, r, o, i], void 0 !== a.ctx.listener.forwardX ? (a.ctx.listener.forwardX.setTargetAtTime(e, Howler.ctx.currentTime, .1), a.ctx.listener.forwardY.setTargetAtTime(n, Howler.ctx.currentTime, .1), a.ctx.listener.forwardZ.setTargetAtTime(t, Howler.ctx.currentTime, .1), a.ctx.listener.upX.setTargetAtTime(r, Howler.ctx.currentTime, .1), a.ctx.listener.upY.setTargetAtTime(o, Howler.ctx.currentTime, .1), a.ctx.listener.upZ.setTargetAtTime(i, Howler.ctx.currentTime, .1)) : a.ctx.listener.setOrientation(e, n, t, r, o, i), a); }, Howl.prototype.init = function (e) { return function (n) { var t = this; return t._orientation = n.orientation || [1, 0, 0], t._stereo = n.stereo || null, t._pos = n.pos || null, t._pannerAttr = { coneInnerAngle: void 0 !== n.coneInnerAngle ? n.coneInnerAngle : 360, coneOuterAngle: void 0 !== n.coneOuterAngle ? n.coneOuterAngle : 360, coneOuterGain: void 0 !== n.coneOuterGain ? n.coneOuterGain : 0, distanceModel: void 0 !== n.distanceModel ? n.distanceModel : 'inverse', maxDistance: void 0 !== n.maxDistance ? n.maxDistance : 1e4, panningModel: void 0 !== n.panningModel ? n.panningModel : 'HRTF', refDistance: void 0 !== n.refDistance ? n.refDistance : 1, rolloffFactor: void 0 !== n.rolloffFactor ? n.rolloffFactor : 1 }, t._onstereo = n.onstereo ? [{ fn: n.onstereo }] : [], t._onpos = n.onpos ? [{ fn: n.onpos }] : [], t._onorientation = n.onorientation ? [{ fn: n.onorientation }] : [], e.call(this, n); }; }(Howl.prototype.init), Howl.prototype.stereo = function (n, t) { var r = this; if (!r._webAudio) return r; if (r._state !== 'loaded') return r._queue.push({ event: 'stereo', action: function () { r.stereo(n, t); } }), r; var o = void 0 === Howler.ctx.createStereoPanner ? 'spatial' : 'stereo'; if (void 0 === t) { if (typeof n != 'number') return r._stereo; r._stereo = n, r._pos = [n, 0, 0]; } for (var i = r._getSoundIds(t), a = 0; a < i.length; a++) { var s = r._soundById(i[a]); if (s) { if (typeof n != 'number') return s._stereo; s._stereo = n, s._pos = [n, 0, 0], s._node && (s._pannerAttr.panningModel = 'equalpower', s._panner && s._panner.pan || e(s, o), o === 'spatial' ? void 0 !== s._panner.positionX ? (s._panner.positionX.setValueAtTime(n, Howler.ctx.currentTime), s._panner.positionY.setValueAtTime(0, Howler.ctx.currentTime), s._panner.positionZ.setValueAtTime(0, Howler.ctx.currentTime)) : s._panner.setPosition(n, 0, 0) : s._panner.pan.setValueAtTime(n, Howler.ctx.currentTime)), r._emit('stereo', s._id); } } return r; }, Howl.prototype.pos = function (n, t, r, o) { var i = this; if (!i._webAudio) return i; if (i._state !== 'loaded') return i._queue.push({ event: 'pos', action: function () { i.pos(n, t, r, o); } }), i; if (t = typeof t != 'number' ? 0 : t, r = typeof r != 'number' ? -.5 : r, void 0 === o) { if (typeof n != 'number') return i._pos; i._pos = [n, t, r]; } for (var a = i._getSoundIds(o), s = 0; s < a.length; s++) { var p = i._soundById(a[s]); if (p) { if (typeof n != 'number') return p._pos; p._pos = [n, t, r], p._node && (p._panner && !p._panner.pan || e(p, 'spatial'), void 0 !== p._panner.positionX ? (p._panner.positionX.setValueAtTime(n, Howler.ctx.currentTime), p._panner.positionY.setValueAtTime(t, Howler.ctx.currentTime), p._panner.positionZ.setValueAtTime(r, Howler.ctx.currentTime)) : p._panner.setPosition(n, t, r)), i._emit('pos', p._id); } } return i; }, Howl.prototype.orientation = function (n, t, r, o) { var i = this; if (!i._webAudio) return i; if (i._state !== 'loaded') return i._queue.push({ event: 'orientation', action: function () { i.orientation(n, t, r, o); } }), i; if (t = typeof t != 'number' ? i._orientation[1] : t, r = typeof r != 'number' ? i._orientation[2] : r, void 0 === o) { if (typeof n != 'number') return i._orientation; i._orientation = [n, t, r]; } for (var a = i._getSoundIds(o), s = 0; s < a.length; s++) { var p = i._soundById(a[s]); if (p) { if (typeof n != 'number') return p._orientation; p._orientation = [n, t, r], p._node && (p._panner || (p._pos || (p._pos = i._pos || [0, 0, -.5]), e(p, 'spatial')), void 0 !== p._panner.orientationX ? (p._panner.orientationX.setValueAtTime(n, Howler.ctx.currentTime), p._panner.orientationY.setValueAtTime(t, Howler.ctx.currentTime), p._panner.orientationZ.setValueAtTime(r, Howler.ctx.currentTime)) : p._panner.setOrientation(n, t, r)), i._emit('orientation', p._id); } } return i; }, Howl.prototype.pannerAttr = function () { var n, t, r, o = this, i = arguments; if (!o._webAudio) return o; if (i.length === 0) return o._pannerAttr; if (i.length === 1) { if (typeof i[0] != 'object') return r = o._soundById(parseInt(i[0], 10)), r ? r._pannerAttr : o._pannerAttr; n = i[0], void 0 === t && (n.pannerAttr || (n.pannerAttr = { coneInnerAngle: n.coneInnerAngle, coneOuterAngle: n.coneOuterAngle, coneOuterGain: n.coneOuterGain, distanceModel: n.distanceModel, maxDistance: n.maxDistance, refDistance: n.refDistance, rolloffFactor: n.rolloffFactor, panningModel: n.panningModel }), o._pannerAttr = { coneInnerAngle: void 0 !== n.pannerAttr.coneInnerAngle ? n.pannerAttr.coneInnerAngle : o._coneInnerAngle, coneOuterAngle: void 0 !== n.pannerAttr.coneOuterAngle ? n.pannerAttr.coneOuterAngle : o._coneOuterAngle, coneOuterGain: void 0 !== n.pannerAttr.coneOuterGain ? n.pannerAttr.coneOuterGain : o._coneOuterGain, distanceModel: void 0 !== n.pannerAttr.distanceModel ? n.pannerAttr.distanceModel : o._distanceModel, maxDistance: void 0 !== n.pannerAttr.maxDistance ? n.pannerAttr.maxDistance : o._maxDistance, refDistance: void 0 !== n.pannerAttr.refDistance ? n.pannerAttr.refDistance : o._refDistance, rolloffFactor: void 0 !== n.pannerAttr.rolloffFactor ? n.pannerAttr.rolloffFactor : o._rolloffFactor, panningModel: void 0 !== n.pannerAttr.panningModel ? n.pannerAttr.panningModel : o._panningModel }); } else i.length === 2 && (n = i[0], t = parseInt(i[1], 10)); for (var a = o._getSoundIds(t), s = 0; s < a.length; s++) if (r = o._soundById(a[s])) { var p = r._pannerAttr; p = { coneInnerAngle: void 0 !== n.coneInnerAngle ? n.coneInnerAngle : p.coneInnerAngle, coneOuterAngle: void 0 !== n.coneOuterAngle ? n.coneOuterAngle : p.coneOuterAngle, coneOuterGain: void 0 !== n.coneOuterGain ? n.coneOuterGain : p.coneOuterGain, distanceModel: void 0 !== n.distanceModel ? n.distanceModel : p.distanceModel, maxDistance: void 0 !== n.maxDistance ? n.maxDistance : p.maxDistance, refDistance: void 0 !== n.refDistance ? n.refDistance : p.refDistance, rolloffFactor: void 0 !== n.rolloffFactor ? n.rolloffFactor : p.rolloffFactor, panningModel: void 0 !== n.panningModel ? n.panningModel : p.panningModel }; var c = r._panner; c ? (c.coneInnerAngle = p.coneInnerAngle, c.coneOuterAngle = p.coneOuterAngle, c.coneOuterGain = p.coneOuterGain, c.distanceModel = p.distanceModel, c.maxDistance = p.maxDistance, c.refDistance = p.refDistance, c.rolloffFactor = p.rolloffFactor, c.panningModel = p.panningModel) : (r._pos || (r._pos = o._pos || [0, 0, -.5]), e(r, 'spatial')); } return o; }, Sound.prototype.init = function (e) { return function () { var n = this, t = n._parent; n._orientation = t._orientation, n._stereo = t._stereo, n._pos = t._pos, n._pannerAttr = t._pannerAttr, e.call(this), n._stereo ? t.stereo(n._stereo) : n._pos && t.pos(n._pos[0], n._pos[1], n._pos[2], n._id); }; }(Sound.prototype.init), Sound.prototype.reset = function (e) { return function () { var n = this, t = n._parent; return n._orientation = t._orientation, n._stereo = t._stereo, n._pos = t._pos, n._pannerAttr = t._pannerAttr, n._stereo ? t.stereo(n._stereo) : n._pos ? t.pos(n._pos[0], n._pos[1], n._pos[2], n._id) : n._panner && (n._panner.disconnect(0), n._panner = void 0, t._refreshBuffer(n)), e.call(this); }; }(Sound.prototype.reset); var e = function (e, n) { n = n || 'spatial', n === 'spatial' ? (e._panner = Howler.ctx.createPanner(), e._panner.coneInnerAngle = e._pannerAttr.coneInnerAngle, e._panner.coneOuterAngle = e._pannerAttr.coneOuterAngle, e._panner.coneOuterGain = e._pannerAttr.coneOuterGain, e._panner.distanceModel = e._pannerAttr.distanceModel, e._panner.maxDistance = e._pannerAttr.maxDistance, e._panner.refDistance = e._pannerAttr.refDistance, e._panner.rolloffFactor = e._pannerAttr.rolloffFactor, e._panner.panningModel = e._pannerAttr.panningModel, void 0 !== e._panner.positionX ? (e._panner.positionX.setValueAtTime(e._pos[0], Howler.ctx.currentTime), e._panner.positionY.setValueAtTime(e._pos[1], Howler.ctx.currentTime), e._panner.positionZ.setValueAtTime(e._pos[2], Howler.ctx.currentTime)) : e._panner.setPosition(e._pos[0], e._pos[1], e._pos[2]), void 0 !== e._panner.orientationX ? (e._panner.orientationX.setValueAtTime(e._orientation[0], Howler.ctx.currentTime), e._panner.orientationY.setValueAtTime(e._orientation[1], Howler.ctx.currentTime), e._panner.orientationZ.setValueAtTime(e._orientation[2], Howler.ctx.currentTime)) : e._panner.setOrientation(e._orientation[0], e._orientation[1], e._orientation[2])) : (e._panner = Howler.ctx.createStereoPanner(), e._panner.pan.setValueAtTime(e._stereo, Howler.ctx.currentTime)), e._panner.connect(e._node), e._paused || e._parent.pause(e._id, !0).play(e._id, !0); }; }();
